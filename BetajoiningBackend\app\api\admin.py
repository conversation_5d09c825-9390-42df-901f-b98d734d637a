import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.dependencies import get_db
from app.schemas.admin import AdminDashboardResponse, AdminUsersResponse, AdminUser
from app.models.user import User
from fastapi.security import OAuth2<PERSON>asswordBearer
from datetime import datetime, timedelta
from app.services.user_service import authenticate_user, create_jwt_for_user, get_user_by_id, promote_user_to_admin, get_bulk_email_recipients
from app.core.security import get_current_admin
from app.schemas.user import UserLogin
from app.tasks.email_tasks import send_bulk_email_task
from pydantic import BaseModel
from app.utils.monitoring import inc_bulk_email_sent, inc_admin_promotion

router = APIRouter(prefix="/admin", tags=["admin"])

class BulkEmailRequest(BaseModel):
    subject: str
    body: str
    min_points: int = 0

class PromoteRequest(BaseModel):
    user_id: int

@router.post("/login")
def admin_login(user_in: UserLogin, db: Session = Depends(get_db)):
    """Admin login. Returns JWT if credentials are valid and user is admin."""
    user = authenticate_user(db, user_in.email, user_in.password)
    if not user or not user.is_admin:
        raise HTTPException(status_code=403, detail="Admin credentials required")
    token = create_jwt_for_user(user)
    return {"access_token": token, "token_type": "bearer", "expires_in": 3600}

@router.get("/dashboard", response_model=AdminDashboardResponse)
def dashboard(db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Get admin dashboard overview with user and platform stats."""
    from app.models.share import ShareEvent
    from sqlalchemy import func

    # Basic user stats
    total_users = db.query(User).count()
    active_users_24h = db.query(User).filter(User.updated_at > datetime.utcnow() - timedelta(hours=24)).count()

    # Share stats for today
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    total_shares_today = db.query(ShareEvent).filter(ShareEvent.created_at >= today_start).count()
    points_distributed_today = db.query(func.sum(ShareEvent.points_earned)).filter(ShareEvent.created_at >= today_start).scalar() or 0

    # Platform breakdown (all time)
    platform_stats = db.query(
        ShareEvent.platform,
        func.count(ShareEvent.id).label('shares'),
        func.sum(ShareEvent.points_earned).label('points')
    ).group_by(ShareEvent.platform).all()

    total_all_shares = sum(stat.shares for stat in platform_stats) or 1  # Avoid division by zero
    platform_breakdown = {}

    for stat in platform_stats:
        platform_name = stat.platform.value if hasattr(stat.platform, 'value') else str(stat.platform)
        platform_breakdown[platform_name] = {
            "shares": stat.shares,
            "percentage": round((stat.shares / total_all_shares) * 100, 1)
        }

    # Ensure all platforms are represented
    for platform in ["facebook", "twitter", "linkedin", "instagram"]:
        if platform not in platform_breakdown:
            platform_breakdown[platform] = {"shares": 0, "percentage": 0}

    # Growth metrics (simplified for now)
    week_ago = datetime.utcnow() - timedelta(days=7)
    new_users_7d = db.query(User).filter(User.created_at >= week_ago).count()

    growth_metrics = {
        "new_users_7d": new_users_7d,
        "user_retention_rate": 0,  # TODO: Implement retention calculation
        "average_session_duration": 0  # TODO: Implement session tracking
    }

    return AdminDashboardResponse(
        overview={
            "total_users": total_users,
            "active_users_24h": active_users_24h,
            "total_shares_today": total_shares_today,
            "points_distributed_today": points_distributed_today
        },
        platform_breakdown=platform_breakdown,
        growth_metrics=growth_metrics
    )

@router.get("/users", response_model=AdminUsersResponse)
def admin_users(db: Session = Depends(get_db), admin=Depends(get_current_admin), page: int = 1, limit: int = 50, search: str = "", sort: str = "points"):
    """List users for admin panel with pagination, search, and sorting."""
    q = db.query(User)
    if search:
        q = q.filter(User.name.ilike(f"%{search}%"))
    if sort == "points":
        q = q.order_by(User.total_points.desc())
    total = q.count()
    users = q.offset((page-1)*limit).limit(limit).all()
    items = [AdminUser(user_id=u.id, name=u.name, email=u.email, points=u.total_points, rank=None, shares_count=u.shares_count, status="active" if u.is_active else "inactive", last_activity=u.updated_at, created_at=u.created_at) for u in users]
    return AdminUsersResponse(users=items, pagination={"page": page, "limit": limit, "total": total, "pages": (total+limit-1)//limit})

@router.post("/send-bulk-email")
def send_bulk_email(req: BulkEmailRequest, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Send a bulk email to all or filtered users (min_points)."""
    users = get_bulk_email_recipients(db, req.min_points)
    emails = [u.email for u in users]
    if not emails:
        raise HTTPException(status_code=404, detail="No users found for criteria")
    send_bulk_email_task.delay(emails, req.subject, req.body)
    inc_bulk_email_sent()
    logging.info(f"Admin {admin['user_id']} sent bulk email to {len(emails)} users.")
    return {"message": f"Bulk email sent to {len(emails)} users (task queued)"}

@router.post("/promote")
def promote_user(req: PromoteRequest, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Promote a user to admin status."""
    user = promote_user_to_admin(db, req.user_id)
    inc_admin_promotion()
    logging.info(f"Admin {admin['user_id']} promoted user {user.email} to admin.")
    return {"message": f"User {user.email} promoted to admin."} 